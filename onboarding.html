<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>欢迎使用</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .gradient-text {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .floating-animation {
            animation: float 3s ease-in-out infinite;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        .slide-up {
            animation: slideUp 0.6s ease-out;
        }
        @keyframes slideUp {
            from { transform: translateY(30px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
    </style>
</head>
<body class="bg-gray-50 h-screen flex flex-col">
    <!-- 状态栏 -->
    <div class="gradient-bg text-white px-5 py-3 flex justify-between items-center text-sm font-semibold">
        <span>9:41</span>
        <span>欢迎</span>
        <div class="flex items-center gap-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-three-quarters text-xs"></i>
        </div>
    </div>

    <!-- 主要内容 -->
    <div class="flex-1 flex flex-col items-center justify-center px-8 py-12">
        <!-- Logo和标题 -->
        <div class="text-center mb-12 slide-up">
            <div class="w-24 h-24 gradient-bg rounded-3xl flex items-center justify-center mx-auto mb-6 floating-animation">
                <i class="fas fa-robot text-white text-4xl"></i>
            </div>
            <h1 class="text-3xl font-bold gradient-text mb-3">AI目标助手</h1>
            <p class="text-gray-600 text-lg">让AI成为你实现梦想的伙伴</p>
        </div>

        <!-- 特性介绍 -->
        <div class="w-full max-w-sm space-y-6 mb-12">
            <div class="flex items-start gap-4 slide-up" style="animation-delay: 0.2s;">
                <div class="w-12 h-12 bg-blue-100 rounded-2xl flex items-center justify-center flex-shrink-0">
                    <i class="fas fa-comments text-blue-600 text-lg"></i>
                </div>
                <div>
                    <h3 class="font-semibold text-gray-900 mb-1">智能对话</h3>
                    <p class="text-sm text-gray-600">用自然语言告诉AI你的目标，它会为你制定个性化计划</p>
                </div>
            </div>

            <div class="flex items-start gap-4 slide-up" style="animation-delay: 0.4s;">
                <div class="w-12 h-12 bg-green-100 rounded-2xl flex items-center justify-center flex-shrink-0">
                    <i class="fas fa-bullseye text-green-600 text-lg"></i>
                </div>
                <div>
                    <h3 class="font-semibold text-gray-900 mb-1">目标规划</h3>
                    <p class="text-sm text-gray-600">AI会根据你的习惯和能力，制定可执行的实现路径</p>
                </div>
            </div>

            <div class="flex items-start gap-4 slide-up" style="animation-delay: 0.6s;">
                <div class="w-12 h-12 bg-purple-100 rounded-2xl flex items-center justify-center flex-shrink-0">
                    <i class="fas fa-heart text-purple-600 text-lg"></i>
                </div>
                <div>
                    <h3 class="font-semibold text-gray-900 mb-1">情感支持</h3>
                    <p class="text-sm text-gray-600">在你需要鼓励时，AI会给你温暖的支持和动力</p>
                </div>
            </div>

            <div class="flex items-start gap-4 slide-up" style="animation-delay: 0.8s;">
                <div class="w-12 h-12 bg-orange-100 rounded-2xl flex items-center justify-center flex-shrink-0">
                    <i class="fas fa-chart-line text-orange-600 text-lg"></i>
                </div>
                <div>
                    <h3 class="font-semibold text-gray-900 mb-1">进度跟踪</h3>
                    <p class="text-sm text-gray-600">实时监控目标进度，及时调整计划确保成功</p>
                </div>
            </div>
        </div>

        <!-- 开始按钮 -->
        <div class="w-full max-w-sm space-y-4 slide-up" style="animation-delay: 1s;">
            <button class="w-full gradient-bg text-white font-semibold py-4 rounded-2xl shadow-lg">
                开始我的目标之旅
            </button>
            <button class="w-full text-gray-600 font-medium py-3">
                已有账户？立即登录
            </button>
        </div>
    </div>

    <!-- 底部装饰 -->
    <div class="px-8 pb-8">
        <div class="flex justify-center space-x-2">
            <div class="w-2 h-2 gradient-bg rounded-full"></div>
            <div class="w-2 h-2 bg-gray-300 rounded-full"></div>
            <div class="w-2 h-2 bg-gray-300 rounded-full"></div>
        </div>
        <p class="text-center text-xs text-gray-500 mt-4">
            通过继续使用，你同意我们的
            <a href="#" class="text-blue-600">服务条款</a>
            和
            <a href="#" class="text-blue-600">隐私政策</a>
        </p>
    </div>
</body>
</html>
