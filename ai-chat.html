<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI目标助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar {
            transform: translateX(-100%);
            transition: transform 0.3s ease-in-out;
        }
        .sidebar.open {
            transform: translateX(0);
        }
        .overlay {
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease-in-out;
        }
        .overlay.show {
            opacity: 1;
            visibility: visible;
        }
        .message-bubble {
            max-width: 85%;
            word-wrap: break-word;
        }
        .user-message {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin-left: auto;
        }
        .ai-message {
            background: #f8fafc;
            color: #1f2937;
            border: 1px solid #e5e7eb;
        }
        .typing-indicator {
            display: flex;
            align-items: center;
            gap: 4px;
        }
        .typing-dot {
            width: 8px;
            height: 8px;
            background: #9ca3af;
            border-radius: 50%;
            animation: typing 1.4s infinite ease-in-out;
        }
        .typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .typing-dot:nth-child(2) { animation-delay: -0.16s; }
        @keyframes typing {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
        }
        .goal-card {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border: 1px solid #0ea5e9;
        }
        .task-item {
            background: #fefefe;
            border: 1px solid #e5e7eb;
        }
        .progress-bar {
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        }
    </style>
</head>
<body class="bg-white h-screen flex">
    <!-- 侧边栏 -->
    <div class="sidebar fixed inset-y-0 left-0 w-80 bg-gray-900 text-white z-50 flex flex-col">
        <!-- 侧边栏头部 -->
        <div class="p-6 border-b border-gray-700">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-bold">AI目标助手</h2>
                <button onclick="toggleSidebar()" class="w-8 h-8 rounded-lg bg-gray-700 flex items-center justify-center hover:bg-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="flex items-center gap-3">
                <div class="w-12 h-12 gradient-bg rounded-full flex items-center justify-center">
                    <span class="text-white font-bold">张</span>
                </div>
                <div>
                    <p class="font-medium">张小明</p>
                    <p class="text-sm text-gray-400">连续打卡 15 天</p>
                </div>
            </div>
        </div>

        <!-- 快速统计 -->
        <div class="p-6 border-b border-gray-700">
            <h3 class="text-sm font-medium text-gray-400 mb-3">今日概览</h3>
            <div class="grid grid-cols-2 gap-4">
                <div class="bg-gray-800 rounded-lg p-3 text-center">
                    <p class="text-2xl font-bold text-blue-400">3</p>
                    <p class="text-xs text-gray-400">进行中目标</p>
                </div>
                <div class="bg-gray-800 rounded-lg p-3 text-center">
                    <p class="text-2xl font-bold text-green-400">8</p>
                    <p class="text-xs text-gray-400">今日任务</p>
                </div>
            </div>
        </div>

        <!-- 对话历史 -->
        <div class="flex-1 overflow-y-auto p-6">
            <h3 class="text-sm font-medium text-gray-400 mb-3">最近对话</h3>
            <div class="space-y-2">
                <div class="p-3 rounded-lg bg-gray-800 hover:bg-gray-700 cursor-pointer">
                    <p class="text-sm font-medium">学吉他目标规划</p>
                    <p class="text-xs text-gray-400">2小时前</p>
                </div>
                <div class="p-3 rounded-lg hover:bg-gray-800 cursor-pointer">
                    <p class="text-sm font-medium">健身计划调整</p>
                    <p class="text-xs text-gray-400">昨天</p>
                </div>
                <div class="p-3 rounded-lg hover:bg-gray-800 cursor-pointer">
                    <p class="text-sm font-medium">阅读习惯养成</p>
                    <p class="text-xs text-gray-400">3天前</p>
                </div>
            </div>
        </div>

        <!-- 侧边栏底部 -->
        <div class="p-6 border-t border-gray-700">
            <div class="space-y-2">
                <button class="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-gray-800 text-left">
                    <i class="fas fa-cog text-gray-400"></i>
                    <span>设置</span>
                </button>
                <button class="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-gray-800 text-left">
                    <i class="fas fa-question-circle text-gray-400"></i>
                    <span>帮助</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 遮罩层 -->
    <div class="overlay fixed inset-0 bg-black bg-opacity-50 z-40" onclick="toggleSidebar()"></div>

    <!-- 主要内容区域 -->
    <div class="flex-1 flex flex-col">
        <!-- 顶部状态栏 -->
        <div class="gradient-bg text-white px-5 py-3 flex justify-between items-center text-sm font-semibold">
            <span>9:41</span>
            <span>AI目标助手</span>
            <div class="flex items-center gap-1">
                <i class="fas fa-signal text-xs"></i>
                <i class="fas fa-wifi text-xs"></i>
                <i class="fas fa-battery-three-quarters text-xs"></i>
            </div>
        </div>

        <!-- 聊天头部 -->
        <div class="bg-white border-b border-gray-100 px-5 py-4 flex items-center gap-3">
            <button onclick="toggleSidebar()" class="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center hover:bg-gray-200">
                <i class="fas fa-bars text-gray-600"></i>
            </button>
            <div class="flex items-center gap-3">
                <div class="w-10 h-10 rounded-full gradient-bg flex items-center justify-center">
                    <i class="fas fa-robot text-white"></i>
                </div>
                <div>
                    <h2 class="font-semibold text-gray-900">AI助手</h2>
                    <p class="text-sm text-green-500 flex items-center gap-1">
                        <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                        在线
                    </p>
                </div>
            </div>
        </div>

        <!-- 聊天内容区域 -->
        <div class="flex-1 overflow-y-auto px-5 py-6 space-y-6">
            <!-- AI欢迎消息 -->
            <div class="flex items-start gap-3">
                <div class="w-8 h-8 rounded-full gradient-bg flex items-center justify-center flex-shrink-0">
                    <i class="fas fa-robot text-white text-sm"></i>
                </div>
                <div class="message-bubble ai-message rounded-2xl px-4 py-3 shadow-sm">
                    <p class="text-sm">你好！我是你的AI目标助手 ✨</p>
                    <p class="text-sm mt-2">我可以帮你：</p>
                    <ul class="text-sm mt-2 space-y-1 text-gray-600">
                        <li>• 制定和管理目标</li>
                        <li>• 规划每日任务</li>
                        <li>• 跟踪进度并提供建议</li>
                        <li>• 在你需要时给予鼓励</li>
                    </ul>
                    <p class="text-sm mt-3">告诉我你想要实现什么目标吧！</p>
                </div>
            </div>

            <!-- 用户消息 -->
            <div class="flex justify-end">
                <div class="message-bubble user-message rounded-2xl px-4 py-3 shadow-sm">
                    <p class="text-sm">我想要在3个月内学会弹吉他，能帮我制定一个计划吗？</p>
                </div>
            </div>

            <!-- AI回复 -->
            <div class="flex items-start gap-3">
                <div class="w-8 h-8 rounded-full gradient-bg flex items-center justify-center flex-shrink-0">
                    <i class="fas fa-robot text-white text-sm"></i>
                </div>
                <div class="message-bubble ai-message rounded-2xl px-4 py-3 shadow-sm">
                    <p class="text-sm">太棒了！学吉他是个很有意思的目标 🎸</p>
                    <p class="text-sm mt-2">我已经为你创建了这个目标，让我来制定一个详细的学习计划：</p>
                    
                    <!-- 目标卡片 -->
                    <div class="goal-card rounded-xl p-4 mt-4">
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center gap-2">
                                <i class="fas fa-guitar text-blue-600"></i>
                                <h4 class="font-semibold text-gray-900">学会弹吉他</h4>
                            </div>
                            <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">3个月</span>
                        </div>
                        <div class="mb-3">
                            <div class="flex justify-between text-xs mb-1">
                                <span>进度</span>
                                <span>0%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="progress-bar h-2 rounded-full" style="width: 0%"></div>
                            </div>
                        </div>
                        <p class="text-xs text-gray-600">目标创建时间：刚刚</p>
                    </div>

                    <p class="text-sm mt-4">我建议按以下阶段进行：</p>
                    
                    <!-- 学习计划 -->
                    <div class="mt-3 space-y-2">
                        <div class="task-item rounded-lg p-3">
                            <div class="flex items-center justify-between mb-2">
                                <p class="text-sm font-medium text-gray-900">第1个月：基础入门</p>
                                <span class="text-xs text-gray-500">30天</span>
                            </div>
                            <ul class="text-xs text-gray-600 space-y-1">
                                <li>• 学习基本和弦（C、G、Am、F）</li>
                                <li>• 练习手指灵活性和按弦力度</li>
                                <li>• 每天练习30分钟</li>
                            </ul>
                        </div>
                        
                        <div class="task-item rounded-lg p-3">
                            <div class="flex items-center justify-between mb-2">
                                <p class="text-sm font-medium text-gray-900">第2个月：技巧提升</p>
                                <span class="text-xs text-gray-500">30天</span>
                            </div>
                            <ul class="text-xs text-gray-600 space-y-1">
                                <li>• 学习扫弦技巧和节拍</li>
                                <li>• 练习和弦转换流畅度</li>
                                <li>• 学习简单歌曲（如《小星星》）</li>
                            </ul>
                        </div>
                        
                        <div class="task-item rounded-lg p-3">
                            <div class="flex items-center justify-between mb-2">
                                <p class="text-sm font-medium text-gray-900">第3个月：实战演练</p>
                                <span class="text-xs text-gray-500">30天</span>
                            </div>
                            <ul class="text-xs text-gray-600 space-y-1">
                                <li>• 完整演奏2-3首喜欢的歌曲</li>
                                <li>• 录制演奏视频分享</li>
                                <li>• 为下一阶段学习做准备</li>
                            </ul>
                        </div>
                    </div>
                    
                    <p class="text-sm mt-4">我还为你安排了今天的第一个任务，要开始吗？</p>
                </div>
            </div>

            <!-- 用户回复 -->
            <div class="flex justify-end">
                <div class="message-bubble user-message rounded-2xl px-4 py-3 shadow-sm">
                    <p class="text-sm">太好了！我想现在就开始，今天应该练习什么？</p>
                </div>
            </div>

            <!-- AI任务安排回复 -->
            <div class="flex items-start gap-3">
                <div class="w-8 h-8 rounded-full gradient-bg flex items-center justify-center flex-shrink-0">
                    <i class="fas fa-robot text-white text-sm"></i>
                </div>
                <div class="message-bubble ai-message rounded-2xl px-4 py-3 shadow-sm">
                    <p class="text-sm">很棒的决定！💪 我已经为你安排了今天的练习任务：</p>

                    <!-- 今日任务卡片 -->
                    <div class="bg-gradient-to-r from-green-50 to-blue-50 rounded-xl p-4 mt-4 border border-green-200">
                        <div class="flex items-center gap-2 mb-3">
                            <i class="fas fa-calendar-check text-green-600"></i>
                            <h4 class="font-semibold text-gray-900">今日任务</h4>
                            <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">30分钟</span>
                        </div>

                        <div class="space-y-3">
                            <div class="flex items-start gap-3">
                                <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                    <span class="text-xs font-bold text-blue-600">1</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">手指热身运动</p>
                                    <p class="text-xs text-gray-600">5分钟 - 活动手指关节，避免受伤</p>
                                </div>
                            </div>

                            <div class="flex items-start gap-3">
                                <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                    <span class="text-xs font-bold text-blue-600">2</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">学习C和弦</p>
                                    <p class="text-xs text-gray-600">15分钟 - 练习手指位置和按弦力度</p>
                                </div>
                            </div>

                            <div class="flex items-start gap-3">
                                <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                    <span class="text-xs font-bold text-blue-600">3</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">简单扫弦练习</p>
                                    <p class="text-xs text-gray-600">10分钟 - 用C和弦练习上下扫弦</p>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4 pt-3 border-t border-green-200">
                            <div class="flex items-center justify-between">
                                <p class="text-xs text-gray-600">
                                    <i class="fas fa-clock mr-1"></i>
                                    建议时间：19:00 - 19:30
                                </p>
                                <button class="text-xs bg-green-600 text-white px-3 py-1 rounded-full hover:bg-green-700">
                                    开始练习
                                </button>
                            </div>
                        </div>
                    </div>

                    <p class="text-sm mt-4">记住，刚开始手指可能会有点疼，这是正常的。如果感到不适就休息一下。</p>
                    <p class="text-sm mt-2">练习完成后记得告诉我，我会记录你的进度并给你鼓励！🎉</p>
                </div>
            </div>

            <!-- 快捷操作建议 -->
            <div class="flex flex-wrap gap-2">
                <button class="bg-white border border-gray-200 rounded-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 shadow-sm">
                    我完成了练习
                </button>
                <button class="bg-white border border-gray-200 rounded-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 shadow-sm">
                    遇到困难了
                </button>
                <button class="bg-white border border-gray-200 rounded-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 shadow-sm">
                    查看我的所有目标
                </button>
                <button class="bg-white border border-gray-200 rounded-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 shadow-sm">
                    需要鼓励
                </button>
            </div>
        </div>

        <!-- 输入区域 -->
        <div class="bg-white border-t border-gray-100 px-5 py-4">
            <div class="flex items-end gap-3">
                <button class="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center hover:bg-gray-200 flex-shrink-0">
                    <i class="fas fa-microphone text-gray-600"></i>
                </button>
                <div class="flex-1 bg-gray-50 rounded-2xl px-4 py-3 flex items-end gap-3 min-h-[44px]">
                    <textarea 
                        placeholder="输入消息..." 
                        class="flex-1 bg-transparent outline-none text-sm resize-none max-h-32"
                        rows="1"
                        onInput="this.style.height = 'auto'; this.style.height = this.scrollHeight + 'px'"
                    ></textarea>
                    <button class="text-gray-400 hover:text-gray-600 flex-shrink-0">
                        <i class="fas fa-smile"></i>
                    </button>
                </div>
                <button class="w-10 h-10 rounded-full gradient-bg flex items-center justify-center hover:opacity-90 flex-shrink-0">
                    <i class="fas fa-paper-plane text-white"></i>
                </button>
            </div>
        </div>
    </div>

    <script>
        function toggleSidebar() {
            const sidebar = document.querySelector('.sidebar');
            const overlay = document.querySelector('.overlay');
            
            sidebar.classList.toggle('open');
            overlay.classList.toggle('show');
        }
    </script>
</body>
</html>
