<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>目标概览</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .progress-ring {
            transform: rotate(-90deg);
        }
        .progress-ring-circle {
            stroke-dasharray: 251.2;
            stroke-dashoffset: 125.6;
            transition: stroke-dashoffset 0.35s;
        }
        .card-shadow {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
    </style>
</head>
<body class="bg-gray-50 h-screen flex flex-col">
    <!-- 状态栏 -->
    <div class="gradient-bg text-white px-5 py-3 flex justify-between items-center text-sm font-semibold">
        <span>9:41</span>
        <span>我的目标</span>
        <div class="flex items-center gap-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-three-quarters text-xs"></i>
        </div>
    </div>

    <!-- 头部区域 -->
    <div class="gradient-bg text-white px-5 py-6">
        <div class="flex items-center justify-between mb-4">
            <div>
                <h1 class="text-2xl font-bold">我的目标</h1>
                <p class="text-blue-100 text-sm">让梦想照进现实</p>
            </div>
            <button class="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                <i class="fas fa-plus text-white"></i>
            </button>
        </div>
        
        <!-- 总体进度 -->
        <div class="bg-white bg-opacity-10 rounded-2xl p-4 backdrop-blur-sm">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-blue-100 text-sm">本月总进度</p>
                    <p class="text-2xl font-bold">68%</p>
                    <p class="text-blue-100 text-xs">3个目标进行中</p>
                </div>
                <div class="relative">
                    <svg class="w-16 h-16 progress-ring">
                        <circle cx="32" cy="32" r="28" stroke="rgba(255,255,255,0.2)" stroke-width="4" fill="none"/>
                        <circle cx="32" cy="32" r="28" stroke="white" stroke-width="4" fill="none" 
                                class="progress-ring-circle" style="stroke-dashoffset: 80.4;"/>
                    </svg>
                    <div class="absolute inset-0 flex items-center justify-center">
                        <span class="text-white text-sm font-bold">68%</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 目标列表 -->
    <div class="flex-1 overflow-y-auto px-5 py-4 space-y-4">
        <!-- 当前目标 -->
        <div class="bg-white rounded-2xl p-5 card-shadow">
            <div class="flex items-start justify-between mb-3">
                <div class="flex items-center gap-3">
                    <div class="w-12 h-12 bg-gradient-to-r from-orange-400 to-pink-400 rounded-xl flex items-center justify-center">
                        <i class="fas fa-guitar text-white text-lg"></i>
                    </div>
                    <div>
                        <h3 class="font-semibold text-gray-900">学会弹吉他</h3>
                        <p class="text-sm text-gray-500">3个月计划</p>
                    </div>
                </div>
                <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">进行中</span>
            </div>
            
            <div class="mb-4">
                <div class="flex justify-between text-sm mb-2">
                    <span class="text-gray-600">进度</span>
                    <span class="font-medium text-gray-900">45%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-gradient-to-r from-orange-400 to-pink-400 h-2 rounded-full" style="width: 45%"></div>
                </div>
            </div>
            
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-600">
                    <i class="fas fa-calendar-alt mr-1"></i>
                    还有 45 天
                </div>
                <button class="text-blue-600 text-sm font-medium">查看详情</button>
            </div>
        </div>

        <!-- 其他目标 -->
        <div class="bg-white rounded-2xl p-5 card-shadow">
            <div class="flex items-start justify-between mb-3">
                <div class="flex items-center gap-3">
                    <div class="w-12 h-12 bg-gradient-to-r from-blue-400 to-purple-400 rounded-xl flex items-center justify-center">
                        <i class="fas fa-dumbbell text-white text-lg"></i>
                    </div>
                    <div>
                        <h3 class="font-semibold text-gray-900">健身塑形</h3>
                        <p class="text-sm text-gray-500">6个月计划</p>
                    </div>
                </div>
                <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">进行中</span>
            </div>
            
            <div class="mb-4">
                <div class="flex justify-between text-sm mb-2">
                    <span class="text-gray-600">进度</span>
                    <span class="font-medium text-gray-900">72%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-gradient-to-r from-blue-400 to-purple-400 h-2 rounded-full" style="width: 72%"></div>
                </div>
            </div>
            
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-600">
                    <i class="fas fa-calendar-alt mr-1"></i>
                    还有 68 天
                </div>
                <button class="text-blue-600 text-sm font-medium">查看详情</button>
            </div>
        </div>

        <div class="bg-white rounded-2xl p-5 card-shadow">
            <div class="flex items-start justify-between mb-3">
                <div class="flex items-center gap-3">
                    <div class="w-12 h-12 bg-gradient-to-r from-green-400 to-teal-400 rounded-xl flex items-center justify-center">
                        <i class="fas fa-book text-white text-lg"></i>
                    </div>
                    <div>
                        <h3 class="font-semibold text-gray-900">阅读100本书</h3>
                        <p class="text-sm text-gray-500">1年计划</p>
                    </div>
                </div>
                <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">暂停</span>
            </div>
            
            <div class="mb-4">
                <div class="flex justify-between text-sm mb-2">
                    <span class="text-gray-600">进度</span>
                    <span class="font-medium text-gray-900">23%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-gradient-to-r from-green-400 to-teal-400 h-2 rounded-full" style="width: 23%"></div>
                </div>
            </div>
            
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-600">
                    <i class="fas fa-calendar-alt mr-1"></i>
                    还有 287 天
                </div>
                <button class="text-blue-600 text-sm font-medium">查看详情</button>
            </div>
        </div>

        <!-- AI建议卡片 -->
        <div class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl p-5 border border-purple-100">
            <div class="flex items-start gap-3">
                <div class="w-10 h-10 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full flex items-center justify-center flex-shrink-0">
                    <i class="fas fa-lightbulb text-white"></i>
                </div>
                <div>
                    <h4 class="font-semibold text-gray-900 mb-2">AI建议</h4>
                    <p class="text-sm text-gray-700 mb-3">
                        你的吉他学习进度很棒！建议今天重点练习 G-C-D 和弦转换，这会让你的演奏更流畅。
                    </p>
                    <button class="text-purple-600 text-sm font-medium">
                        查看更多建议 <i class="fas fa-arrow-right ml-1"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="bg-white border-t border-gray-200 px-5 py-2">
        <div class="flex justify-around items-center">
            <button class="flex flex-col items-center py-2 text-gray-400">
                <i class="fas fa-comments text-lg"></i>
                <span class="text-xs mt-1">AI助手</span>
            </button>
            <button class="flex flex-col items-center py-2 text-blue-600">
                <i class="fas fa-bullseye text-lg"></i>
                <span class="text-xs mt-1">目标</span>
            </button>
            <button class="flex flex-col items-center py-2 text-gray-400">
                <i class="fas fa-tasks text-lg"></i>
                <span class="text-xs mt-1">任务</span>
            </button>
            <button class="flex flex-col items-center py-2 text-gray-400">
                <i class="fas fa-user text-lg"></i>
                <span class="text-xs mt-1">我的</span>
            </button>
        </div>
    </div>
</body>
</html>
