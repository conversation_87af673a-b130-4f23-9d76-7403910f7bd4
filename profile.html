<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人档案</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-shadow {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        .achievement-badge {
            background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
        }
    </style>
</head>
<body class="bg-gray-50 h-screen flex flex-col">
    <!-- 状态栏 -->
    <div class="gradient-bg text-white px-5 py-3 flex justify-between items-center text-sm font-semibold">
        <span>9:41</span>
        <span>个人档案</span>
        <div class="flex items-center gap-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-three-quarters text-xs"></i>
        </div>
    </div>

    <!-- 个人信息头部 -->
    <div class="gradient-bg text-white px-5 py-6">
        <div class="flex items-center gap-4 mb-6">
            <div class="w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=80&h=80&fit=crop&crop=face" 
                     alt="头像" class="w-16 h-16 rounded-full object-cover">
            </div>
            <div class="flex-1">
                <h1 class="text-2xl font-bold">张小明</h1>
                <p class="text-blue-100 text-sm">音乐爱好者 · 健身达人</p>
                <div class="flex items-center gap-2 mt-2">
                    <div class="bg-white bg-opacity-20 rounded-full px-3 py-1">
                        <span class="text-xs">连续打卡 15 天</span>
                    </div>
                </div>
            </div>
            <button class="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                <i class="fas fa-cog text-white"></i>
            </button>
        </div>
        
        <!-- 成就统计 -->
        <div class="grid grid-cols-3 gap-3">
            <div class="bg-white bg-opacity-10 rounded-xl p-3 text-center backdrop-blur-sm">
                <p class="text-2xl font-bold">12</p>
                <p class="text-blue-100 text-xs">已完成目标</p>
            </div>
            <div class="bg-white bg-opacity-10 rounded-xl p-3 text-center backdrop-blur-sm">
                <p class="text-2xl font-bold">156</p>
                <p class="text-blue-100 text-xs">总任务数</p>
            </div>
            <div class="bg-white bg-opacity-10 rounded-xl p-3 text-center backdrop-blur-sm">
                <p class="text-2xl font-bold">89%</p>
                <p class="text-blue-100 text-xs">完成率</p>
            </div>
        </div>
    </div>

    <!-- 内容区域 -->
    <div class="flex-1 overflow-y-auto px-5 py-4 space-y-4">
        <!-- 成就徽章 -->
        <div class="bg-white rounded-2xl p-5 card-shadow">
            <div class="flex items-center justify-between mb-4">
                <h3 class="font-semibold text-gray-900 flex items-center gap-2">
                    <i class="fas fa-trophy text-yellow-500"></i>
                    成就徽章
                </h3>
                <button class="text-blue-600 text-sm">查看全部</button>
            </div>
            
            <div class="grid grid-cols-4 gap-3">
                <div class="text-center">
                    <div class="w-12 h-12 achievement-badge rounded-full flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-fire text-white"></i>
                    </div>
                    <p class="text-xs text-gray-600">连续打卡</p>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 bg-gradient-to-r from-green-400 to-teal-400 rounded-full flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-target text-white"></i>
                    </div>
                    <p class="text-xs text-gray-600">目标达人</p>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-star text-white"></i>
                    </div>
                    <p class="text-xs text-gray-600">完美主义</p>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-lock text-gray-400"></i>
                    </div>
                    <p class="text-xs text-gray-400">待解锁</p>
                </div>
            </div>
        </div>

        <!-- AI了解的我 -->
        <div class="bg-white rounded-2xl p-5 card-shadow">
            <div class="flex items-center gap-3 mb-4">
                <div class="w-10 h-10 gradient-bg rounded-full flex items-center justify-center">
                    <i class="fas fa-robot text-white"></i>
                </div>
                <h3 class="font-semibold text-gray-900">AI了解的我</h3>
            </div>
            
            <div class="space-y-3">
                <div class="bg-blue-50 rounded-xl p-4">
                    <h4 class="font-medium text-blue-900 mb-2">性格特点</h4>
                    <p class="text-sm text-blue-700">你是一个有毅力的人，喜欢制定计划并坚持执行。对音乐和运动都很有热情。</p>
                </div>
                
                <div class="bg-green-50 rounded-xl p-4">
                    <h4 class="font-medium text-green-900 mb-2">最佳学习时间</h4>
                    <p class="text-sm text-green-700">早上 6:30-8:00 和晚上 19:00-21:00 是你效率最高的时间段。</p>
                </div>
                
                <div class="bg-purple-50 rounded-xl p-4">
                    <h4 class="font-medium text-purple-900 mb-2">激励方式</h4>
                    <p class="text-sm text-purple-700">你更喜欢具体的进度反馈和阶段性成就感，适合设定小目标。</p>
                </div>
            </div>
        </div>

        <!-- 习惯分析 -->
        <div class="bg-white rounded-2xl p-5 card-shadow">
            <h3 class="font-semibold text-gray-900 mb-4">习惯分析</h3>
            
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                        <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-running text-green-600 text-sm"></i>
                        </div>
                        <div>
                            <p class="font-medium text-gray-900">晨跑</p>
                            <p class="text-xs text-gray-500">坚持 45 天</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-sm font-medium text-green-600">95%</p>
                        <p class="text-xs text-gray-500">完成率</p>
                    </div>
                </div>
                
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                        <div class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-guitar text-orange-600 text-sm"></i>
                        </div>
                        <div>
                            <p class="font-medium text-gray-900">吉他练习</p>
                            <p class="text-xs text-gray-500">坚持 23 天</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-sm font-medium text-orange-600">78%</p>
                        <p class="text-xs text-gray-500">完成率</p>
                    </div>
                </div>
                
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-book text-blue-600 text-sm"></i>
                        </div>
                        <div>
                            <p class="font-medium text-gray-900">阅读</p>
                            <p class="text-xs text-gray-500">坚持 67 天</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-sm font-medium text-blue-600">85%</p>
                        <p class="text-xs text-gray-500">完成率</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 最近活动 -->
        <div class="bg-white rounded-2xl p-5 card-shadow">
            <h3 class="font-semibold text-gray-900 mb-4">最近活动</h3>
            
            <div class="space-y-3">
                <div class="flex items-center gap-3 p-3 bg-gray-50 rounded-xl">
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-check text-green-600 text-sm"></i>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-900">完成了"练习吉他基本和弦"</p>
                        <p class="text-xs text-gray-500">2小时前</p>
                    </div>
                </div>
                
                <div class="flex items-center gap-3 p-3 bg-gray-50 rounded-xl">
                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-trophy text-blue-600 text-sm"></i>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-900">获得"连续打卡15天"徽章</p>
                        <p class="text-xs text-gray-500">昨天</p>
                    </div>
                </div>
                
                <div class="flex items-center gap-3 p-3 bg-gray-50 rounded-xl">
                    <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-bullseye text-purple-600 text-sm"></i>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-900">设定了新目标"学会弹吉他"</p>
                        <p class="text-xs text-gray-500">3天前</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="bg-white border-t border-gray-200 px-5 py-2">
        <div class="flex justify-around items-center">
            <button class="flex flex-col items-center py-2 text-gray-400">
                <i class="fas fa-comments text-lg"></i>
                <span class="text-xs mt-1">AI助手</span>
            </button>
            <button class="flex flex-col items-center py-2 text-gray-400">
                <i class="fas fa-bullseye text-lg"></i>
                <span class="text-xs mt-1">目标</span>
            </button>
            <button class="flex flex-col items-center py-2 text-gray-400">
                <i class="fas fa-tasks text-lg"></i>
                <span class="text-xs mt-1">任务</span>
            </button>
            <button class="flex flex-col items-center py-2 text-blue-600">
                <i class="fas fa-user text-lg"></i>
                <span class="text-xs mt-1">我的</span>
            </button>
        </div>
    </div>
</body>
</html>
