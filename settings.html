<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设置</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-shadow {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        .toggle-switch {
            width: 44px;
            height: 24px;
            background: #e5e7eb;
            border-radius: 12px;
            position: relative;
            cursor: pointer;
            transition: background 0.3s;
        }
        .toggle-switch.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .toggle-switch::after {
            content: '';
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            position: absolute;
            top: 2px;
            left: 2px;
            transition: transform 0.3s;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        .toggle-switch.active::after {
            transform: translateX(20px);
        }
    </style>
</head>
<body class="bg-gray-50 h-screen flex flex-col">
    <!-- 状态栏 -->
    <div class="gradient-bg text-white px-5 py-3 flex justify-between items-center text-sm font-semibold">
        <span>9:41</span>
        <span>设置</span>
        <div class="flex items-center gap-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-three-quarters text-xs"></i>
        </div>
    </div>

    <!-- 头部 -->
    <div class="gradient-bg text-white px-5 py-6">
        <div class="flex items-center gap-4">
            <button class="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                <i class="fas fa-arrow-left text-white"></i>
            </button>
            <div>
                <h1 class="text-2xl font-bold">设置</h1>
                <p class="text-blue-100 text-sm">个性化你的AI助手</p>
            </div>
        </div>
    </div>

    <!-- 设置内容 -->
    <div class="flex-1 overflow-y-auto px-5 py-4 space-y-4">
        <!-- AI助手设置 -->
        <div class="bg-white rounded-2xl p-5 card-shadow">
            <h3 class="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <i class="fas fa-robot text-blue-600"></i>
                AI助手设置
            </h3>
            
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="font-medium text-gray-900">智能提醒</p>
                        <p class="text-sm text-gray-500">AI会在合适的时间提醒你完成任务</p>
                    </div>
                    <div class="toggle-switch active"></div>
                </div>
                
                <div class="flex items-center justify-between">
                    <div>
                        <p class="font-medium text-gray-900">个性化建议</p>
                        <p class="text-sm text-gray-500">根据你的习惯提供定制化建议</p>
                    </div>
                    <div class="toggle-switch active"></div>
                </div>
                
                <div class="flex items-center justify-between">
                    <div>
                        <p class="font-medium text-gray-900">情绪支持</p>
                        <p class="text-sm text-gray-500">在你需要时提供鼓励和支持</p>
                    </div>
                    <div class="toggle-switch active"></div>
                </div>
                
                <div class="flex items-center justify-between">
                    <div>
                        <p class="font-medium text-gray-900">语音交互</p>
                        <p class="text-sm text-gray-500">启用语音输入和语音回复</p>
                    </div>
                    <div class="toggle-switch"></div>
                </div>
            </div>
        </div>

        <!-- 通知设置 -->
        <div class="bg-white rounded-2xl p-5 card-shadow">
            <h3 class="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <i class="fas fa-bell text-orange-600"></i>
                通知设置
            </h3>
            
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="font-medium text-gray-900">任务提醒</p>
                        <p class="text-sm text-gray-500">在任务开始前提醒你</p>
                    </div>
                    <div class="toggle-switch active"></div>
                </div>
                
                <div class="flex items-center justify-between">
                    <div>
                        <p class="font-medium text-gray-900">目标进度</p>
                        <p class="text-sm text-gray-500">定期汇报目标完成情况</p>
                    </div>
                    <div class="toggle-switch active"></div>
                </div>
                
                <div class="flex items-center justify-between">
                    <div>
                        <p class="font-medium text-gray-900">成就通知</p>
                        <p class="text-sm text-gray-500">获得新成就时通知你</p>
                    </div>
                    <div class="toggle-switch active"></div>
                </div>
                
                <div class="border-t border-gray-100 pt-4">
                    <button class="flex items-center justify-between w-full">
                        <div>
                            <p class="font-medium text-gray-900">免打扰时间</p>
                            <p class="text-sm text-gray-500">22:00 - 07:00</p>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- 隐私与安全 -->
        <div class="bg-white rounded-2xl p-5 card-shadow">
            <h3 class="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <i class="fas fa-shield-alt text-green-600"></i>
                隐私与安全
            </h3>
            
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="font-medium text-gray-900">数据同步</p>
                        <p class="text-sm text-gray-500">在设备间同步你的数据</p>
                    </div>
                    <div class="toggle-switch active"></div>
                </div>
                
                <div class="flex items-center justify-between">
                    <div>
                        <p class="font-medium text-gray-900">使用分析</p>
                        <p class="text-sm text-gray-500">帮助改进产品体验</p>
                    </div>
                    <div class="toggle-switch"></div>
                </div>
                
                <div class="border-t border-gray-100 pt-4 space-y-3">
                    <button class="flex items-center justify-between w-full">
                        <div class="flex items-center gap-3">
                            <i class="fas fa-download text-blue-600"></i>
                            <p class="font-medium text-gray-900">导出数据</p>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </button>
                    
                    <button class="flex items-center justify-between w-full">
                        <div class="flex items-center gap-3">
                            <i class="fas fa-trash text-red-600"></i>
                            <p class="font-medium text-gray-900">删除账户</p>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- 外观设置 -->
        <div class="bg-white rounded-2xl p-5 card-shadow">
            <h3 class="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <i class="fas fa-palette text-purple-600"></i>
                外观设置
            </h3>
            
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="font-medium text-gray-900">深色模式</p>
                        <p class="text-sm text-gray-500">在弱光环境下保护眼睛</p>
                    </div>
                    <div class="toggle-switch"></div>
                </div>
                
                <div class="border-t border-gray-100 pt-4">
                    <button class="flex items-center justify-between w-full">
                        <div>
                            <p class="font-medium text-gray-900">主题颜色</p>
                            <p class="text-sm text-gray-500">蓝紫渐变</p>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="w-6 h-6 gradient-bg rounded-full"></div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                    </button>
                </div>
            </div>
        </div>

        <!-- 关于 -->
        <div class="bg-white rounded-2xl p-5 card-shadow">
            <h3 class="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <i class="fas fa-info-circle text-gray-600"></i>
                关于
            </h3>
            
            <div class="space-y-3">
                <button class="flex items-center justify-between w-full">
                    <p class="font-medium text-gray-900">版本信息</p>
                    <div class="flex items-center gap-2">
                        <span class="text-sm text-gray-500">v1.0.0</span>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                </button>
                
                <button class="flex items-center justify-between w-full">
                    <p class="font-medium text-gray-900">用户协议</p>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </button>
                
                <button class="flex items-center justify-between w-full">
                    <p class="font-medium text-gray-900">隐私政策</p>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </button>
                
                <button class="flex items-center justify-between w-full">
                    <p class="font-medium text-gray-900">意见反馈</p>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </button>
            </div>
        </div>

        <!-- 退出登录 -->
        <div class="bg-white rounded-2xl p-5 card-shadow">
            <button class="w-full text-red-600 font-medium py-2">
                退出登录
            </button>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="bg-white border-t border-gray-200 px-5 py-2">
        <div class="flex justify-around items-center">
            <button class="flex flex-col items-center py-2 text-gray-400">
                <i class="fas fa-comments text-lg"></i>
                <span class="text-xs mt-1">AI助手</span>
            </button>
            <button class="flex flex-col items-center py-2 text-gray-400">
                <i class="fas fa-bullseye text-lg"></i>
                <span class="text-xs mt-1">目标</span>
            </button>
            <button class="flex flex-col items-center py-2 text-gray-400">
                <i class="fas fa-tasks text-lg"></i>
                <span class="text-xs mt-1">任务</span>
            </button>
            <button class="flex flex-col items-center py-2 text-gray-400">
                <i class="fas fa-user text-lg"></i>
                <span class="text-xs mt-1">我的</span>
            </button>
        </div>
    </div>
</body>
</html>
