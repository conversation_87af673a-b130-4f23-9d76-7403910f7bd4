<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI目标管理助手 - 原型展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .phone-container {
            width: 393px;
            height: 852px;
            background: #000;
            border-radius: 47px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            position: relative;
        }
        .phone-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 150px;
            height: 30px;
            background: #000;
            border-radius: 0 0 20px 20px;
            z-index: 10;
        }
        .phone-screen {
            width: 100%;
            height: 100%;
            background: #f8fafc;
            border-radius: 39px;
            overflow: hidden;
            position: relative;
        }
        .status-bar {
            height: 47px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        .prototype-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 40px;
            padding: 40px;
            background: #f1f5f9;
            min-height: 100vh;
        }
        .prototype-item {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .prototype-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 20px;
            text-align: center;
        }
        iframe {
            border: none;
            width: 100%;
            height: 100%;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .gradient-text {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="text-center py-8">
        <h1 class="text-4xl font-bold gradient-text mb-4">AI目标管理助手</h1>
        <p class="text-gray-600 text-lg">高保真原型界面展示</p>
    </div>

    <div class="prototype-grid">
        <!-- AI对话界面 -->
        <div class="prototype-item">
            <h2 class="prototype-title">AI助手对话界面</h2>
            <div class="phone-container">
                <div class="phone-screen">
                    <iframe src="chat.html"></iframe>
                </div>
            </div>
        </div>

        <!-- 目标概览界面 -->
        <div class="prototype-item">
            <h2 class="prototype-title">目标概览界面</h2>
            <div class="phone-container">
                <div class="phone-screen">
                    <iframe src="goals.html"></iframe>
                </div>
            </div>
        </div>

        <!-- 任务列表界面 -->
        <div class="prototype-item">
            <h2 class="prototype-title">任务管理界面</h2>
            <div class="phone-container">
                <div class="phone-screen">
                    <iframe src="tasks.html"></iframe>
                </div>
            </div>
        </div>

        <!-- 个人档案界面 -->
        <div class="prototype-item">
            <h2 class="prototype-title">个人档案界面</h2>
            <div class="phone-container">
                <div class="phone-screen">
                    <iframe src="profile.html"></iframe>
                </div>
            </div>
        </div>

        <!-- 设置界面 -->
        <div class="prototype-item">
            <h2 class="prototype-title">设置界面</h2>
            <div class="phone-container">
                <div class="phone-screen">
                    <iframe src="settings.html"></iframe>
                </div>
            </div>
        </div>

        <!-- 引导界面 -->
        <div class="prototype-item">
            <h2 class="prototype-title">引导界面</h2>
            <div class="phone-container">
                <div class="phone-screen">
                    <iframe src="onboarding.html"></iframe>
                </div>
            </div>
        </div>
    </div>

    <div class="text-center py-8 text-gray-500">
        <p>© 2024 AI目标管理助手 - 原型设计</p>
    </div>
</body>
</html>
