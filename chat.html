<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI助手对话</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .message-bubble {
            max-width: 80%;
            word-wrap: break-word;
        }
        .user-message {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin-left: auto;
        }
        .ai-message {
            background: white;
            color: #1f2937;
            border: 1px solid #e5e7eb;
        }
        .typing-indicator {
            display: flex;
            align-items: center;
            gap: 4px;
        }
        .typing-dot {
            width: 8px;
            height: 8px;
            background: #9ca3af;
            border-radius: 50%;
            animation: typing 1.4s infinite ease-in-out;
        }
        .typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .typing-dot:nth-child(2) { animation-delay: -0.16s; }
        @keyframes typing {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
        }
    </style>
</head>
<body class="bg-gray-50 h-screen flex flex-col">
    <!-- 状态栏 -->
    <div class="gradient-bg text-white px-5 py-3 flex justify-between items-center text-sm font-semibold">
        <span>9:41</span>
        <span>AI助手</span>
        <div class="flex items-center gap-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-three-quarters text-xs"></i>
        </div>
    </div>

    <!-- 聊天头部 -->
    <div class="bg-white border-b border-gray-200 px-5 py-4 flex items-center gap-3">
        <div class="w-10 h-10 rounded-full gradient-bg flex items-center justify-center">
            <i class="fas fa-robot text-white"></i>
        </div>
        <div>
            <h2 class="font-semibold text-gray-900">小助手</h2>
            <p class="text-sm text-green-500 flex items-center gap-1">
                <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                在线
            </p>
        </div>
        <div class="ml-auto">
            <button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
                <i class="fas fa-ellipsis-h text-gray-600"></i>
            </button>
        </div>
    </div>

    <!-- 聊天内容 -->
    <div class="flex-1 overflow-y-auto px-5 py-4 space-y-4">
        <!-- AI欢迎消息 -->
        <div class="flex items-start gap-3">
            <div class="w-8 h-8 rounded-full gradient-bg flex items-center justify-center flex-shrink-0">
                <i class="fas fa-robot text-white text-sm"></i>
            </div>
            <div class="message-bubble ai-message rounded-2xl px-4 py-3 shadow-sm">
                <p class="text-sm">你好！我是你的AI目标管理助手 ✨</p>
                <p class="text-sm mt-2">我会帮你制定个性化的目标计划，并在实现过程中给你鼓励和支持。让我们开始吧！</p>
            </div>
        </div>

        <!-- 用户消息 -->
        <div class="flex justify-end">
            <div class="message-bubble user-message rounded-2xl px-4 py-3 shadow-sm">
                <p class="text-sm">我想要在3个月内学会弹吉他</p>
            </div>
        </div>

        <!-- AI回复 -->
        <div class="flex items-start gap-3">
            <div class="w-8 h-8 rounded-full gradient-bg flex items-center justify-center flex-shrink-0">
                <i class="fas fa-robot text-white text-sm"></i>
            </div>
            <div class="message-bubble ai-message rounded-2xl px-4 py-3 shadow-sm">
                <p class="text-sm">太棒了！学吉他是个很有意思的目标 🎸</p>
                <p class="text-sm mt-2">让我为你制定一个循序渐进的学习计划：</p>
                <div class="mt-3 space-y-2">
                    <div class="bg-blue-50 rounded-lg p-3">
                        <p class="text-sm font-medium text-blue-900">第1个月：基础入门</p>
                        <p class="text-xs text-blue-700 mt-1">• 学习基本和弦 • 练习手指灵活性</p>
                    </div>
                    <div class="bg-purple-50 rounded-lg p-3">
                        <p class="text-sm font-medium text-purple-900">第2个月：技巧提升</p>
                        <p class="text-xs text-purple-700 mt-1">• 学习扫弦技巧 • 练习简单歌曲</p>
                    </div>
                    <div class="bg-green-50 rounded-lg p-3">
                        <p class="text-sm font-medium text-green-900">第3个月：实战演练</p>
                        <p class="text-xs text-green-700 mt-1">• 完整演奏歌曲 • 录制作品分享</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快捷操作建议 -->
        <div class="flex flex-wrap gap-2 mt-4">
            <button class="bg-white border border-gray-200 rounded-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                制定今日任务
            </button>
            <button class="bg-white border border-gray-200 rounded-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                查看进度
            </button>
            <button class="bg-white border border-gray-200 rounded-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                需要鼓励
            </button>
        </div>

        <!-- 正在输入指示器 -->
        <div class="flex items-start gap-3">
            <div class="w-8 h-8 rounded-full gradient-bg flex items-center justify-center flex-shrink-0">
                <i class="fas fa-robot text-white text-sm"></i>
            </div>
            <div class="bg-white rounded-2xl px-4 py-3 shadow-sm">
                <div class="typing-indicator">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 输入区域 -->
    <div class="bg-white border-t border-gray-200 px-5 py-4">
        <div class="flex items-center gap-3">
            <button class="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
                <i class="fas fa-microphone text-gray-600"></i>
            </button>
            <div class="flex-1 bg-gray-100 rounded-full px-4 py-3 flex items-center gap-3">
                <input type="text" placeholder="输入消息..." class="flex-1 bg-transparent outline-none text-sm">
                <button class="text-gray-400">
                    <i class="fas fa-smile"></i>
                </button>
            </div>
            <button class="w-10 h-10 rounded-full gradient-bg flex items-center justify-center">
                <i class="fas fa-paper-plane text-white"></i>
            </button>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="bg-white border-t border-gray-200 px-5 py-2">
        <div class="flex justify-around items-center">
            <button class="flex flex-col items-center py-2 text-blue-600">
                <i class="fas fa-comments text-lg"></i>
                <span class="text-xs mt-1">AI助手</span>
            </button>
            <button class="flex flex-col items-center py-2 text-gray-400">
                <i class="fas fa-bullseye text-lg"></i>
                <span class="text-xs mt-1">目标</span>
            </button>
            <button class="flex flex-col items-center py-2 text-gray-400">
                <i class="fas fa-tasks text-lg"></i>
                <span class="text-xs mt-1">任务</span>
            </button>
            <button class="flex flex-col items-center py-2 text-gray-400">
                <i class="fas fa-user text-lg"></i>
                <span class="text-xs mt-1">我的</span>
            </button>
        </div>
    </div>
</body>
</html>
