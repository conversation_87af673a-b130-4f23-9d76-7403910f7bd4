<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-shadow {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        .task-checkbox {
            appearance: none;
            width: 20px;
            height: 20px;
            border: 2px solid #d1d5db;
            border-radius: 4px;
            position: relative;
            cursor: pointer;
        }
        .task-checkbox:checked {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-color: #667eea;
        }
        .task-checkbox:checked::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 12px;
            font-weight: bold;
        }
    </style>
</head>
<body class="bg-gray-50 h-screen flex flex-col">
    <!-- 状态栏 -->
    <div class="gradient-bg text-white px-5 py-3 flex justify-between items-center text-sm font-semibold">
        <span>9:41</span>
        <span>今日任务</span>
        <div class="flex items-center gap-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-three-quarters text-xs"></i>
        </div>
    </div>

    <!-- 头部区域 -->
    <div class="gradient-bg text-white px-5 py-6">
        <div class="flex items-center justify-between mb-4">
            <div>
                <h1 class="text-2xl font-bold">今日任务</h1>
                <p class="text-blue-100 text-sm">2024年12月30日 星期一</p>
            </div>
            <button class="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                <i class="fas fa-calendar-alt text-white"></i>
            </button>
        </div>
        
        <!-- 今日统计 -->
        <div class="grid grid-cols-3 gap-3">
            <div class="bg-white bg-opacity-10 rounded-xl p-3 text-center backdrop-blur-sm">
                <p class="text-2xl font-bold">8</p>
                <p class="text-blue-100 text-xs">总任务</p>
            </div>
            <div class="bg-white bg-opacity-10 rounded-xl p-3 text-center backdrop-blur-sm">
                <p class="text-2xl font-bold">5</p>
                <p class="text-blue-100 text-xs">已完成</p>
            </div>
            <div class="bg-white bg-opacity-10 rounded-xl p-3 text-center backdrop-blur-sm">
                <p class="text-2xl font-bold">3</p>
                <p class="text-blue-100 text-xs">待完成</p>
            </div>
        </div>
    </div>

    <!-- 任务列表 -->
    <div class="flex-1 overflow-y-auto px-5 py-4 space-y-4">
        <!-- 优先级任务 -->
        <div class="bg-white rounded-2xl p-5 card-shadow">
            <div class="flex items-center justify-between mb-4">
                <h3 class="font-semibold text-gray-900 flex items-center gap-2">
                    <i class="fas fa-star text-yellow-500"></i>
                    优先任务
                </h3>
                <span class="text-xs text-gray-500">2/3 完成</span>
            </div>
            
            <div class="space-y-3">
                <div class="flex items-center gap-3 p-3 bg-gray-50 rounded-xl">
                    <input type="checkbox" class="task-checkbox" checked>
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-900 line-through">练习吉他基本和弦 30分钟</p>
                        <p class="text-xs text-gray-500">09:00 - 09:30</p>
                    </div>
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-guitar text-green-600 text-xs"></i>
                    </div>
                </div>
                
                <div class="flex items-center gap-3 p-3 bg-gray-50 rounded-xl">
                    <input type="checkbox" class="task-checkbox" checked>
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-900 line-through">晨跑 5公里</p>
                        <p class="text-xs text-gray-500">06:30 - 07:00</p>
                    </div>
                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-running text-blue-600 text-xs"></i>
                    </div>
                </div>
                
                <div class="flex items-center gap-3 p-3 bg-orange-50 rounded-xl border border-orange-200">
                    <input type="checkbox" class="task-checkbox">
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-900">阅读《吉他自学三月通》第3章</p>
                        <p class="text-xs text-orange-600">19:00 - 20:00 · 即将开始</p>
                    </div>
                    <div class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-book text-orange-600 text-xs"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 常规任务 -->
        <div class="bg-white rounded-2xl p-5 card-shadow">
            <div class="flex items-center justify-between mb-4">
                <h3 class="font-semibold text-gray-900">常规任务</h3>
                <span class="text-xs text-gray-500">3/5 完成</span>
            </div>
            
            <div class="space-y-3">
                <div class="flex items-center gap-3 p-3 bg-gray-50 rounded-xl">
                    <input type="checkbox" class="task-checkbox" checked>
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-900 line-through">喝水 2L</p>
                        <p class="text-xs text-gray-500">全天</p>
                    </div>
                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-tint text-blue-600 text-xs"></i>
                    </div>
                </div>
                
                <div class="flex items-center gap-3 p-3 bg-gray-50 rounded-xl">
                    <input type="checkbox" class="task-checkbox" checked>
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-900 line-through">冥想 10分钟</p>
                        <p class="text-xs text-gray-500">22:00 - 22:10</p>
                    </div>
                    <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-leaf text-purple-600 text-xs"></i>
                    </div>
                </div>
                
                <div class="flex items-center gap-3 p-3 bg-gray-50 rounded-xl">
                    <input type="checkbox" class="task-checkbox" checked>
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-900 line-through">整理房间</p>
                        <p class="text-xs text-gray-500">14:00 - 14:30</p>
                    </div>
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-home text-green-600 text-xs"></i>
                    </div>
                </div>
                
                <div class="flex items-center gap-3 p-3 bg-gray-50 rounded-xl">
                    <input type="checkbox" class="task-checkbox">
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-900">准备明天的工作计划</p>
                        <p class="text-xs text-gray-500">21:00 - 21:30</p>
                    </div>
                    <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-clipboard-list text-gray-600 text-xs"></i>
                    </div>
                </div>
                
                <div class="flex items-center gap-3 p-3 bg-gray-50 rounded-xl">
                    <input type="checkbox" class="task-checkbox">
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-900">给家人打电话</p>
                        <p class="text-xs text-gray-500">20:00 - 20:30</p>
                    </div>
                    <div class="w-8 h-8 bg-pink-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-phone text-pink-600 text-xs"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- AI鼓励消息 -->
        <div class="bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl p-5 border border-green-100">
            <div class="flex items-start gap-3">
                <div class="w-10 h-10 bg-gradient-to-r from-green-400 to-blue-400 rounded-full flex items-center justify-center flex-shrink-0">
                    <i class="fas fa-heart text-white"></i>
                </div>
                <div>
                    <h4 class="font-semibold text-gray-900 mb-2">今日表现很棒！</h4>
                    <p class="text-sm text-gray-700 mb-3">
                        你已经完成了 5/8 个任务，完成率达到 62.5%！继续保持这个节奏，你一定能实现所有目标 💪
                    </p>
                    <div class="flex gap-2">
                        <button class="bg-green-100 text-green-700 text-xs px-3 py-1 rounded-full">
                            <i class="fas fa-thumbs-up mr-1"></i>
                            谢谢鼓励
                        </button>
                        <button class="bg-blue-100 text-blue-700 text-xs px-3 py-1 rounded-full">
                            <i class="fas fa-comments mr-1"></i>
                            和AI聊聊
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="bg-white border-t border-gray-200 px-5 py-2">
        <div class="flex justify-around items-center">
            <button class="flex flex-col items-center py-2 text-gray-400">
                <i class="fas fa-comments text-lg"></i>
                <span class="text-xs mt-1">AI助手</span>
            </button>
            <button class="flex flex-col items-center py-2 text-gray-400">
                <i class="fas fa-bullseye text-lg"></i>
                <span class="text-xs mt-1">目标</span>
            </button>
            <button class="flex flex-col items-center py-2 text-blue-600">
                <i class="fas fa-tasks text-lg"></i>
                <span class="text-xs mt-1">任务</span>
            </button>
            <button class="flex flex-col items-center py-2 text-gray-400">
                <i class="fas fa-user text-lg"></i>
                <span class="text-xs mt-1">我的</span>
            </button>
        </div>
    </div>
</body>
</html>
